/* Resume Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.resume-modal {
  background: var(--bg-primary);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  max-width: 1400px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
  transition: background-color 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-primary);
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: white;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Modal Content */
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

.modal-content-left,
.modal-content-right {
  display: flex;
  flex-direction: column;
}

.modal-content-full {
  grid-column: 1 / -1;
}

.modal-section {
  margin-bottom: 32px;
}

.modal-section:last-child {
  margin-bottom: 0;
}

/* Section Header (Personal Info) */
.section-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 24px;
}

.resume-avatar-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.personal-info {
  flex: 1;
}

.candidate-name {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.candidate-title {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--text-secondary);
  line-height: 1.3;
}

.match-score-large {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.score-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.score-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--success);
}

.score-bar {
  flex: 1;
  min-width: 120px;
  height: 8px;
  background: var(--border-primary);
  border-radius: 4px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success) 0%, #047857 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Contact Actions */
.contact-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.contact-btn:hover {
  border-color: var(--accent-primary);
  background: var(--bg-tertiary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.email-btn:hover {
  border-color: var(--success);
  color: var(--success);
}

.phone-btn:hover {
  border-color: var(--error);
  color: var(--error);
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--border-primary);
}

.section-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Section Content */
.section-content {
  color: var(--text-secondary);
  line-height: 1.6;
}

.summary-text,
.experience-text,
.education-text {
  margin: 0;
  font-size: 1rem;
}

/* Skills Grid */
.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag-large {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.skill-tag-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.skill-tag-large.technical {
  background: linear-gradient(135deg, var(--success) 0%, #047857 100%);
}

.skill-tag-large.technical:hover {
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.technical-skills {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-primary);
}

.technical-skills h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Raw Data Info */
.raw-data-info {
  background: var(--bg-tertiary);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--accent-primary);
}

.raw-data-info p {
  margin: 0 0 8px 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.raw-data-info p:last-child {
  margin-bottom: 0;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 32px;
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
  background: var(--bg-secondary);
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: 1px solid #dc2626;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

/* Dark theme compatibility for danger button */
[data-theme="dark"] .btn-danger {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  border-color: #ef4444;
}

[data-theme="dark"] .btn-danger:hover {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 8px 25px rgba(248, 113, 113, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .modal-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .modal-content-full {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .modal-backdrop {
    padding: 10px;
  }

  .resume-modal {
    max-height: 95vh;
    width: 98%;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 20px;
  }

  .modal-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .candidate-name {
    font-size: 1.5rem;
  }

  .match-score-large {
    justify-content: center;
  }

  .contact-actions {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Detailed Sections */
.experience-list,
.education-list,
.projects-list,
.certifications-list,
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.experience-item,
.education-item,
.project-item,
.certification-item {
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--accent-primary);
  transition: all 0.3s ease;
}

.experience-item h4,
.education-item h4,
.project-item h4,
.certification-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.company-name,
.institution {
  font-weight: 500;
  color: var(--accent-primary);
  margin: 4px 0;
}

.duration,
.graduation-year,
.marks {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 4px 0;
}

.description,
.responsibilities p {
  margin: 8px 0 0 0;
  line-height: 1.5;
  color: var(--text-secondary);
}

.responsibilities {
  margin-top: 8px;
}

.responsibilities strong {
  color: var(--text-primary);
}

.total-experience {
  margin-top: 16px;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  font-weight: 500;
  color: var(--accent-primary);
}

.languages-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.language-tag {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.achievement-item {
  background: var(--bg-secondary);
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid var(--warning);
  transition: all 0.3s ease;
}

.achievement-item p {
  margin: 0;
  color: var(--text-secondary);
}
