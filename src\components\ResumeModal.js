import React, { useRef } from 'react';
import { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward, FiTrash2 } from 'react-icons/fi';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { toast } from 'react-toastify';
import { API_ENDPOINTS } from '../config/api';
import './ResumeModal.css';

const ResumeModal = ({ resume, isOpen, onClose, onDelete }) => {
  const modalContentRef = useRef(null);

  if (!isOpen || !resume) return null;

  const handleDownload = async () => {
    try {
      // Download the original PDF from MongoDB only
      const resumeId = resume._id || resume.id;

      if (!resumeId) {
        toast.error('Resume ID not found. Cannot download PDF.');
        return;
      }

      try {
        const response = await fetch(`http://localhost:8003/api/resumes/${resumeId}/download`);

        if (response.ok) {
          // PDF download successful
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // Get filename from response headers or use default
          const contentDisposition = response.headers.get('Content-Disposition');
          let filename = `${resume.name.replace(/\s+/g, '_')}_Resume.pdf`;

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1].replace(/['"]/g, '');
            }
          }

          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          toast.success(`Downloaded PDF for ${resume.name}`);
          return;
        } else {
          // Handle error response
          const errorData = await response.json();
          toast.error(`Failed to download PDF: ${errorData.error || 'Unknown error'}`);
          return;
        }
      } catch (pdfError) {
        console.error('PDF download failed:', pdfError);
        toast.error(`Failed to download PDF: ${pdfError.message}`);
        return;
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleEmailClick = () => {
    window.open(`mailto:${resume.email}`, '_blank');
  };

  const handlePhoneClick = () => {
    window.open(`tel:${resume.phone}`, '_blank');
  };

  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete the resume for ${resume.name}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:8003/api/resumes/${resume.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success(`Resume for ${resume.name} has been deleted successfully`);
        onClose(); // Close the modal
        if (onDelete) {
          onDelete(resume.id); // Notify parent component
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete resume');
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error(`Failed to delete resume: ${error.message}`);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="resume-modal" ref={modalContentRef}>
        <div className="modal-header">
          <div className="modal-title">
            <FiUser size={24} />
            <h2>Resume Details</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <FiX size={24} />
          </button>
        </div>

        <div className="modal-content">
          {/* Personal Information Section - Full Width */}
          <div className="modal-content-full">
            <div className="modal-section">
              <div className="section-header">
                <div className="resume-avatar-large">
                  <FiUser size={32} />
                </div>
                <div className="personal-info">
                  <h1 className="candidate-name">{resume.name}</h1>
                  <h2 className="candidate-title">{resume.title}</h2>
                  <div className="match-score-large">
                    <span className="score-label">Match Score:</span>
                    <span className="score-value">{resume.score}%</span>
                    <div className="score-bar">
                      <div
                        className="score-fill"
                        style={{ width: `${resume.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                <div className="contact-actions">
                  <button
                    className="contact-btn email-btn"
                    onClick={handleEmailClick}
                    title={`Email ${resume.name}`}
                  >
                    <FiMail size={16} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} />
                  </button>
                  <button
                    className="contact-btn phone-btn"
                    onClick={handlePhoneClick}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={16} />
                    <span>{resume.phone}</span>
                    <FiExternalLink size={12} />
                  </button>
                  <div className="contact-info location-info">
                    <FiMapPin size={16} />
                    <span>{resume.location}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Left Column */}
          <div className="modal-content-left">
            {/* Summary/Objective Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiUser size={20} />
                <h3>Professional Summary</h3>
              </div>
              <div className="section-content">
                <p className="summary-text">{resume.summary}</p>
              </div>
            </div>

            {/* Experience Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiBriefcase size={20} />
                <h3>Work Experience</h3>
              </div>
              <div className="section-content">
                {resume.rawData?.Resume?.WorkExperience && resume.rawData.Resume.WorkExperience.length > 0 ? (
                  <div className="experience-list">
                    {resume.rawData.Resume.WorkExperience.map((exp, index) => (
                      <div key={index} className="experience-item">
                        <h4>{exp.Role || exp.JobTitle || exp.Position || 'Position'}</h4>
                        <p className="company-name">{exp.CompanyName || exp.Company || exp.Organization || 'Company'}</p>
                        <p className="duration">
                          {exp.StartYear && exp.EndYear
                            ? `${exp.StartYear} - ${exp.EndYear === 0 ? 'Present' : exp.EndYear}`
                            : exp.Duration || exp.Years || 'Duration not specified'
                          }
                        </p>
                        <div className="description">
                          <strong>Responsibilities:</strong>
                          <p>{
                            (() => {
                              const responsibility = exp['Description/Responsibility'] || exp.Description || exp.Responsibilities;
                              // Check if responsibility is empty, null, undefined, or the string "0"
                              if (!responsibility || responsibility === '0' || responsibility === 0) {
                                return 'Not Mentioned';
                              }
                              return responsibility;
                            })()
                          }</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="experience-text">{resume.experience || 'No work experience available'}</p>
                )}
                {resume.rawData?.Resume?.TotalWorkExperienceInYears && (
                  <p className="total-experience">
                    <strong>Total Experience:</strong> {resume.rawData.Resume.TotalWorkExperienceInYears} years
                  </p>
                )}
              </div>
            </div>

            {/* Projects Section */}
            {resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Projects</h3>
                </div>
                <div className="section-content">
                  <div className="projects-list">
                    {resume.rawData.Resume.Projects.map((project, index) => (
                      <div key={index} className="project-item">
                        <h4>{project.Name || project.Title || `Project ${index + 1}`}</h4>
                        {project.Description && <p>{project.Description}</p>}
                        {project.Technologies && (
                          <p><strong>Technologies:</strong> {project.Technologies}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="modal-content-right">
            {/* Education Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiBookOpen size={20} />
                <h3>Education</h3>
              </div>
              <div className="section-content">
                {resume.rawData?.Resume?.Education && resume.rawData.Resume.Education.length > 0 ? (
                  <div className="education-list">
                    {resume.rawData.Resume.Education.map((edu, index) => (
                      <div key={index} className="education-item">
                        <h4>{edu.Degree || 'Degree'}</h4>
                        <p className="institution">{edu.Institution || edu.School || 'Institution'}</p>
                        <p className="graduation-year">
                          {edu.GraduationYear || edu.Year || 'Year not specified'}
                        </p>
                        <p className="marks">
                          Marks: {edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? `${edu['GPA/Marks/%']}%` : 'Not Mentioned'}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="education-text">{resume.education || 'No education information available'}</p>
                )}
              </div>
            </div>

            {/* Skills Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiAward size={20} />
                <h3>Skills & Technologies</h3>
              </div>
              <div className="section-content">
                {resume.skills && resume.skills.length > 0 ? (
                  <div className="skills-grid">
                    {resume.skills.map((skill, index) => (
                      <span key={index} className="skill-tag-large">{skill}</span>
                    ))}
                  </div>
                ) : (
                  <p>No skills listed</p>
                )}
                {/* Show Technical Skills separately if they exist */}
                {resume.rawData?.Resume?.TechnicalSkills && resume.rawData.Resume.TechnicalSkills.length > 0 && (
                  <div className="technical-skills">
                    <h4>Technical Skills:</h4>
                    <div className="skills-grid">
                      {resume.rawData.Resume.TechnicalSkills.map((skill, index) => (
                        <span key={index} className="skill-tag-large technical">{skill}</span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Languages Section */}
            {resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Languages</h3>
                </div>
                <div className="section-content">
                  <div className="languages-list">
                    {resume.rawData.Resume.Languages.map((lang, index) => (
                      <span key={index} className="language-tag">{lang}</span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Certifications Section */}
            {resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Certifications</h3>
                </div>
                <div className="section-content">
                  <div className="certifications-list">
                    {resume.rawData.Resume.Certifications.map((cert, index) => (
                      <div key={index} className="certification-item">
                        <h4>{cert.CertificationName || cert.Name || cert.Title || `Certification ${index + 1}`}</h4>
                        {cert.IssuingOrganization && <p>Issued by: {cert.IssuingOrganization}</p>}
                        {cert.IssueDate && <p>Date: {cert.IssueDate}</p>}
                        {cert.ExpiryDate && <p>Expires: {cert.ExpiryDate}</p>}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Achievements Section */}
            {resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Achievements</h3>
                </div>
                <div className="section-content">
                  <div className="achievements-list">
                    {resume.rawData.Resume.Achievements.map((achievement, index) => (
                      <div key={index} className="achievement-item">
                        {typeof achievement === 'string' ? (
                          <p>{achievement}</p>
                        ) : (
                          <div>
                            <h4>{achievement.AchievementName || achievement.Name || `Achievement ${index + 1}`}</h4>
                            {achievement.IssueDate && <p>Date: {achievement.IssueDate}</p>}
                            {achievement.IssuingOrganization && <p>Organization: {achievement.IssuingOrganization}</p>}
                            {achievement.Description && <p>{achievement.Description}</p>}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Raw Data Section (for debugging) */}
            {resume.rawData && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Additional Information</h3>
                </div>
                <div className="section-content">
                  <div className="raw-data-info">
                    <p><strong>Database ID:</strong> {resume.rawData._id || 'N/A'}</p>
                    <p><strong>Data Source:</strong> {resume.rawData.Resume ? 'MongoDB' : 'Mock Data'}</p>
                    {resume.rawData.Resume?.PersonalInformation?.LinkedIn && (
                      <p><strong>LinkedIn:</strong> {resume.rawData.Resume.PersonalInformation.LinkedIn}</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button className="btn btn-danger" onClick={handleDelete}>
            <FiTrash2 size={16} />
            Delete Resume
          </button>
          <button className="btn btn-primary" onClick={handleDownload}>
            <FiDownload size={16} />
            Download Resume
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResumeModal;
