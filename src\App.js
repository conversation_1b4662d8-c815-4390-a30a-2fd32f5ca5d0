import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import UploadResume from './pages/UploadResume';
import SearchResumes from './pages/SearchResumes';
// DISABLED: All Resumes page - To re-enable, uncomment the line below
// import AllResumes from './pages/AllResumes';
import Favorites from './pages/Favorites';
import ActivityLog from './pages/ActivityLog';
import Profile from './pages/Profile';
import Support from './pages/Support';

import './App.css';

function AppContent() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentUser] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: null
  });

  const navigate = useNavigate();

  const handleFavoritesClick = () => {
    navigate('/favorites');
  };

  return (
    <div className="app">
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />
      <div className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Header user={currentUser} onFavoritesClick={handleFavoritesClick} />
        <div className="content">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/upload" element={<UploadResume />} />
            <Route path="/search" element={<SearchResumes />} />
            {/* DISABLED: All Resumes route - To re-enable, uncomment the line below */}
            {/* <Route path="/resumes" element={<AllResumes />} /> */}
            <Route path="/favorites" element={<Favorites />} />
            <Route path="/activity" element={<ActivityLog />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/support" element={<Support />} />
          </Routes>
        </div>
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3001}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
}

function App() {
  return (
    <ThemeProvider>
      <NotificationProvider>
        <Router>
          <AppContent />
        </Router>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;
