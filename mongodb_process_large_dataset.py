#!/usr/bin/env python3
"""
Script to process large datasets (200+ files) with parallel processing
"""

import asyncio
import os
import sys
from datetime import datetime
from mongoDBInsertion import process_documents_from_folder

async def process_large_folder():
    """Process a large folder with optimized settings for 200+ files."""
    
    # Configuration
    folder_path = r"H:\AI Data\21_ResumesByMsKopalJain\New folder"
    
    if not folder_path:
        print("No folder path provided. Exiting.")
        return
    
    if not os.path.exists(folder_path):
        print(f"Folder does not exist: {folder_path}")
        return
    
    # Count files first
    supported_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    files = [f for f in os.listdir(folder_path) 
             if any(f.lower().endswith(ext) for ext in supported_extensions)]
    
    print(f"\nFound {len(files)} processable files in the folder")
    
    if len(files) == 0:
        print("No processable files found. Exiting.")
        return
    
    # Recommend worker count based on file count
    if len(files) < 50:
        recommended_workers = 6
    elif len(files) < 100:
        recommended_workers = 8
    elif len(files) < 200:
        recommended_workers = 12
    else:
        recommended_workers = 10
    
    print(f"Recommended worker count for {len(files)} files: {recommended_workers}")
    
    # Get user preferences
    workers =  recommended_workers
    
    database = "dbProductionV2"
    
    collection = "collectionResumeV2"
    
    print("\n" + "=" * 60)
    print("LARGE DATASET PROCESSING CONFIGURATION")
    print("=" * 60)
    print(f"Folder: {folder_path}")
    print(f"Files to process: {len(files)}")
    print(f"Workers: {workers}")
    print(f"Database: {database}")
    print(f"Collection: {collection}")
    print("=" * 60)

    
    print(f"\nStarting processing at {datetime.now()}")
    print("This may take a while for large datasets...")
    print("You can monitor progress in the logs.")
    
    try:
        # Process the folder
        results = await process_documents_from_folder(
            folder_path=folder_path,
            database_name=database,
            collection_name=collection,
            n_workers=workers
        )
        
        # Display detailed results
        print("\n" + "=" * 60)
        print("PROCESSING COMPLETED!")
        print("=" * 60)
        
        print(f"Total files found: {results.get('total_files', 0)}")
        print(f"Successfully processed: {results.get('processed_successfully', 0)}")
        print(f"Failed: {results.get('failed_files', 0)}")
        print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
        print(f"Processing time: {results.get('processing_time', 'N/A')}")
        
        # Calculate performance metrics
        if results.get('processed_successfully', 0) > 0:
            start_time = results.get('start_time')
            end_time = results.get('end_time')
            if start_time and end_time:
                total_seconds = (end_time - start_time).total_seconds()
                throughput = results['processed_successfully'] / total_seconds if total_seconds > 0 else 0
                avg_time = total_seconds / results['processed_successfully'] if results['processed_successfully'] > 0 else 0
                
                print(f"Throughput: {throughput:.2f} files/second")
                print(f"Average time per file: {avg_time:.2f} seconds")
        
        # Show failed files if any
        if results.get('failed_files_list'):
            print(f"\nFailed files ({len(results['failed_files_list'])}):")
            for i, filename in enumerate(results['failed_files_list'][:10], 1):
                print(f"  {i}. {filename}")
            if len(results['failed_files_list']) > 10:
                print(f"  ... and {len(results['failed_files_list']) - 10} more")
        
        # Show duplicate files if any
        if results.get('duplicate_files_list'):
            print(f"\nDuplicate files skipped ({len(results['duplicate_files_list'])}):")
            for i, filename in enumerate(results['duplicate_files_list'][:5], 1):
                print(f"  {i}. {filename}")
            if len(results['duplicate_files_list']) > 5:
                print(f"  ... and {len(results['duplicate_files_list']) - 5} more")
        
        # Success rate
        total_attempted = results.get('processed_successfully', 0) + results.get('failed_files', 0)
        if total_attempted > 0:
            success_rate = (results.get('processed_successfully', 0) / total_attempted) * 100
            print(f"\nSuccess rate: {success_rate:.1f}%")
        
        print("=" * 60)
        
        # Offer to show sample processed data
        if results.get('processed_successfully', 0) > 0:
            show_sample = input("\nShow sample of processed data? (y/N): ").strip().lower()
            if show_sample == 'y':
                await show_sample_data(database, collection)
        
    except KeyboardInterrupt:
        print("\n\nProcessing interrupted by user.")
        print("Some files may have been processed before interruption.")
    except Exception as e:
        print(f"\nError during processing: {e}")
        import traceback
        traceback.print_exc()


async def show_sample_data(database_name: str, collection_name: str):
    """Show a sample of processed data from MongoDB."""
    try:
        from mongoDBInsertion import list_processed_documents
        
        print(f"\nSample processed documents from {database_name}.{collection_name}:")
        print("-" * 60)
        
        documents = list_processed_documents(database_name, collection_name)
        
        if documents:
            # Show first 3 documents
            for i, doc in enumerate(documents[:3], 1):
                print(f"{i}. ID: {doc['_id']}")
                print(f"   Filename: {doc.get('original_filename', 'N/A')}")
                print(f"   Processed: {doc.get('timestamp', 'N/A')}")
                print(f"   PDF File ID: {doc.get('pdf_file_id', 'N/A')}")
                print(f"   Checksum: {doc.get('checksum', 'N/A')[:16]}...")
                print("-" * 60)
            
            if len(documents) > 3:
                print(f"... and {len(documents) - 3} more documents")
        else:
            print("No documents found in the collection.")
            
    except Exception as e:
        print(f"Error retrieving sample data: {e}")


def main():
    """Main function."""
    print("MongoDB Document Insertion - Large Dataset Processor")
    print("Optimized for processing 200+ files with parallel workers")
    print("=" * 60)
    
    try:
        asyncio.run(process_large_folder())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
