import React, { useState, useEffect } from 'react';
import { <PERSON>Heart, FiDownload, <PERSON>Eye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiTrash2 } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ResumeModal from '../components/ResumeModal';
import './SearchResumes.css'; // Reuse the same styles

const Favorites = () => {
  const [favoriteResumes, setFavoriteResumes] = useState([]);
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load favorites from localStorage
  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = () => {
    try {
      setIsLoading(true);
      const favorites = localStorage.getItem('favoriteResumes');
      if (favorites) {
        const parsedFavorites = JSON.parse(favorites);
        const favoriteArray = Object.values(parsedFavorites);
        setFavoriteResumes(favoriteArray);
        console.log('Loaded favorites:', favoriteArray.length, 'items');
      } else {
        setFavoriteResumes([]);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavoriteResumes([]);
      toast.error('Error loading favorite resumes');
    } finally {
      setIsLoading(false);
    }
  };

  const removeFavorite = (resumeId) => {
    try {
      const favorites = localStorage.getItem('favoriteResumes');
      if (favorites) {
        const parsedFavorites = JSON.parse(favorites);
        delete parsedFavorites[resumeId];
        localStorage.setItem('favoriteResumes', JSON.stringify(parsedFavorites));
        
        // Update local state
        setFavoriteResumes(Object.values(parsedFavorites));
        
        // Dispatch event to update header count
        window.dispatchEvent(new Event('favoritesUpdated'));
        
        toast.success('Removed from favorites');
      }
    } catch (error) {
      console.error('Error removing favorite:', error);
      toast.error('Error removing from favorites');
    }
  };

  const handleView = (resume) => {
    setSelectedResume(resume);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const handleDownload = async (resume) => {
    try {
      if (resume.rawData && resume.rawData._id) {
        const response = await fetch(`http://localhost:8003/download/${resume.rawData._id}`);
        if (response.ok) {
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${resume.name}_Resume.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          toast.success(`Downloaded ${resume.name}'s resume`);
        } else {
          throw new Error('Download failed');
        }
      } else {
        toast.warning('Resume file not available for download');
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleEmailClick = (email, name) => {
    window.open(`mailto:${email}?subject=Regarding Your Resume&body=Dear ${name},%0D%0A%0D%0A`, '_blank');
    toast.info(`Opening email client to contact ${name}`);
  };

  const handlePhoneClick = (phone, name) => {
    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {
      window.open(`tel:${phone}`, '_blank');
    } else {
      navigator.clipboard.writeText(phone);
      toast.info(`Phone number copied: ${phone}`);
    }
  };

  const handleExportAll = () => {
    if (favoriteResumes.length === 0) {
      toast.warning('No favorite resumes to export');
      return;
    }

    try {
      const exportData = favoriteResumes.map(resume => ({
        name: resume.name,
        title: resume.title,
        email: resume.email,
        phone: resume.phone,
        location: resume.location,
        experience: resume.experience,
        education: resume.education,
        skills: resume.skills.join(', '),
        summary: resume.summary,
        dateAdded: resume.dateAdded || 'Unknown'
      }));

      const csvContent = [
        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Date Added',
        ...exportData.map(row =>
          Object.values(row).map(value =>
            `"${String(value).replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Favorite_Resumes_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Exported ${favoriteResumes.length} favorite resumes to CSV`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export favorites. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="search-page">
        <div className="page-header">
          <h1>Favorite Resumes</h1>
          <p>Loading your favorite resumes...</p>
        </div>
        <div className="loading-state">
          <div className="loading-spinner" />
        </div>
      </div>
    );
  }

  return (
    <div className="search-page">
      <div className="page-header">
        <h1>Favorite Resumes</h1>
        <p>Your pinned resumes for quick access</p>
      </div>

      {favoriteResumes.length > 0 ? (
        <div className="results-section">
          <div className="results-header">
            <h2>Favorite Resumes ({favoriteResumes.length})</h2>
            <div className="results-actions">
              <button
                className="btn btn-secondary"
                onClick={handleExportAll}
                title={`Export ${favoriteResumes.length} favorites to CSV`}
              >
                <FiDownload size={16} />
                Export All ({favoriteResumes.length})
              </button>
            </div>
          </div>

          <div className="results-grid">
            {favoriteResumes.map((resume) => (
              <div key={resume.id} className="resume-card">
                <div className="resume-header">
                  <div className="resume-avatar">
                    <FiUser size={24} />
                  </div>
                  <div className="resume-info">
                    <h3 className="resume-name">{resume.name}</h3>
                    <p className="resume-title">{resume.title}</p>
                  </div>
                  <button
                    className="favorite-btn favorited"
                    onClick={() => removeFavorite(resume.id)}
                    title="Remove from favorites"
                  >
                    <FiHeart size={16} />
                  </button>
                </div>

                <div className="resume-details">
                  <div className="detail-item location-item">
                    <FiMapPin size={14} />
                    <span>{resume.location}</span>
                  </div>
                  <div
                    className="detail-item email-item clickable"
                    onClick={() => handleEmailClick(resume.email, resume.name)}
                    title={`Send email to ${resume.name}`}
                  >
                    <FiMail size={14} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                  <div
                    className="detail-item phone-item clickable"
                    onClick={() => handlePhoneClick(resume.phone, resume.name)}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={14} />
                    <span>{resume.phone}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                </div>

                <div className="resume-content">
                  <div className="content-row">
                    <div className="content-section">
                      <h4>Experience</h4>
                      <p>{resume.experience}</p>
                    </div>
                    <div className="content-section">
                      <h4>Education</h4>
                      <p>{resume.education}</p>
                    </div>
                  </div>
                  <div className="content-section">
                    <h4>Skills</h4>
                    <div className="skills-list">
                      {resume.skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                  {resume.dateAdded && (
                    <div className="content-section">
                      <h4>Added to Favorites</h4>
                      <p>{new Date(resume.dateAdded).toLocaleDateString()}</p>
                    </div>
                  )}
                </div>

                <div className="resume-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleView(resume)}
                  >
                    <FiEye size={16} />
                    View Details
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleDownload(resume)}
                  >
                    <FiDownload size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="empty-state">
          <div className="empty-icon">
            <FiHeart size={48} />
          </div>
          <h3>No Favorite Resumes</h3>
          <p>You haven't added any resumes to your favorites yet.</p>
          <div className="search-examples">
            <h4>To add favorites:</h4>
            <ul>
              <li>Go to Search or All Resumes page</li>
              <li>Click the heart icon on any resume card</li>
              <li>Your favorites will appear here</li>
            </ul>
          </div>
        </div>
      )}

      {/* Resume Details Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default Favorites;
