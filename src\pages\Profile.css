/* Profile.css */
.profile-page {
  animation: fadeIn 0.5s ease-in;
  max-width: 600px;
  margin: 0 auto;
}

.profile-container {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-card {
  padding: 32px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-primary);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.field-value {
  font-size: 16px;
  color: var(--text-secondary);
  padding: 12px 0;
  border-bottom: 1px solid var(--border-primary);
}

.form-group .input {
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-group .input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea.input {
  resize: vertical;
  min-height: 80px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-page {
    max-width: 100%;
  }
  
  .profile-card {
    padding: 24px;
  }
  
  .profile-header {
    flex-direction: column;
    gap: 20px;
    align-items: center;
    text-align: center;
  }
  
  .edit-actions {
    flex-direction: column;
    width: 100%;
  }
}
