{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pym<PERSON>o\n", "  Obtaining dependency information for pymongo from https://files.pythonhosted.org/packages/1d/2d/044b8511853c8d439817dfee4b1d99060fb76cb08c980877fcb6a6bc1da1/pymongo-4.11.1-cp311-cp311-win_amd64.whl.metadata\n", "  Downloading pymongo-4.11.1-cp311-cp311-win_amd64.whl.metadata (22 kB)\n", "Collecting dnspython<3.0.0,>=1.16.0 (from pymongo)\n", "  Obtaining dependency information for dnspython<3.0.0,>=1.16.0 from https://files.pythonhosted.org/packages/68/1b/e0a87d256e40e8c888847551b20a017a6b98139178505dc7ffb96f04e954/dnspython-2.7.0-py3-none-any.whl.metadata\n", "  Downloading dnspython-2.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Downloading pymongo-4.11.1-cp311-cp311-win_amd64.whl (831 kB)\n", "   ---------------------------------------- 0.0/831.6 kB ? eta -:--:--\n", "   ------------- -------------------------- 276.5/831.6 kB 5.7 MB/s eta 0:00:01\n", "   --------------------------------------  829.4/831.6 kB 10.5 MB/s eta 0:00:01\n", "   ---------------------------------------- 831.6/831.6 kB 8.7 MB/s eta 0:00:00\n", "Downloading dnspython-2.7.0-py3-none-any.whl (313 kB)\n", "   ---------------------------------------- 0.0/313.6 kB ? eta -:--:--\n", "   --------------------------------------- 313.6/313.6 kB 19.0 MB/s eta 0:00:00\n", "Installing collected packages: dnspython, pymongo\n", "Successfully installed dnspython-2.7.0 pymongo-4.11.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 23.2.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["pip install pymongo"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MongoDB Connection Successful!\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://localhost:27017/\")\n", "\n", "# Create or access a database\n", "db = client[\"test_database\"]\n", "\n", "# Create or access a collection\n", "collection = db[\"test_collection\"]\n", "\n", "print(\"MongoDB Connection Successful!\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inserted Data: {'_id': ObjectId('67b88a063313970193611c4f'), 'name': '<PERSON>', 'age': 25, 'city': 'Los Angeles'}\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://localhost:27017/\")\n", "\n", "# Create or access a database\n", "db = client[\"test_database\"]\n", "\n", "# Create or access a collection\n", "collection = db[\"test_collection\"]\n", "\n", "# Insert a test document\n", "test_data = {\"name\": \"<PERSON>\", \"age\": 25, \"city\": \"Los Angeles\"}\n", "inserted_id = collection.insert_one(test_data).inserted_id\n", "\n", "# Retrieve the inserted document\n", "retrieved_data = collection.find_one({\"_id\": inserted_id})\n", "\n", "# Print retrieved data\n", "print(\"Inserted Data:\", retrieved_data)\n", "\n", "# Close the connection\n", "client.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MongoDb"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to MongoDB\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "def get_database(db_name=\"test_database\"):\n", "    client = MongoClient(\"mongodb://localhost:27017/\")\n", "    return client[db_name]\n", "\n", "# Example usage\n", "db = get_database()\n", "print(\"Connected to MongoDB\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "InvalidOperation", "evalue": "Cannot use MongoClient after close", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mInvalidOperation\u001b[0m                          Traceback (most recent call last)", "Cell \u001b[1;32mIn[6], line 7\u001b[0m\n\u001b[0;32m      5\u001b[0m db \u001b[38;5;241m=\u001b[39m get_database()\n\u001b[0;32m      6\u001b[0m query \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAlice\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[1;32m----> 7\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mfind_one_document\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcollection\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFound Document:\u001b[39m\u001b[38;5;124m\"\u001b[39m, result)\n", "Cell \u001b[1;32mIn[6], line 2\u001b[0m, in \u001b[0;36mfind_one_document\u001b[1;34m(collection, query)\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mfind_one_document\u001b[39m(collection, query\u001b[38;5;241m=\u001b[39m{}):\n\u001b[1;32m----> 2\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcollection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfind_one\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\collection.py:1755\u001b[0m, in \u001b[0;36mCollection.find_one\u001b[1;34m(self, filter, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1753\u001b[0m     \u001b[38;5;28mfilter\u001b[39m \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_id\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mfilter\u001b[39m}\n\u001b[0;32m   1754\u001b[0m cursor \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfind(\u001b[38;5;28mfilter\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m-> 1755\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresult\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcursor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlimit\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[0;32m   1756\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mreturn\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresult\u001b[49m\n\u001b[0;32m   1757\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1281\u001b[0m, in \u001b[0;36mCursor.__next__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1280\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__next__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _DocumentType:\n\u001b[1;32m-> 1281\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1257\u001b[0m, in \u001b[0;36mCursor.next\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1255\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_empty:\n\u001b[0;32m   1256\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m\n\u001b[1;32m-> 1257\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m   1258\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data\u001b[38;5;241m.\u001b[39mpopleft()\n\u001b[0;32m   1259\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1205\u001b[0m, in \u001b[0;36mCursor._refresh\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1183\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\n\u001b[0;32m   1184\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPassing a \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhint\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is required when using the min/max query\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1185\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m option to ensure the query utilizes the correct index\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1186\u001b[0m         )\n\u001b[0;32m   1187\u001b[0m     q \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_class(\n\u001b[0;32m   1188\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_flags,\n\u001b[0;32m   1189\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_collection\u001b[38;5;241m.\u001b[39mdatabase\u001b[38;5;241m.\u001b[39mname,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1203\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust,\n\u001b[0;32m   1204\u001b[0m     )\n\u001b[1;32m-> 1205\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_message\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1206\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_id:  \u001b[38;5;66;03m# Get More\u001b[39;00m\n\u001b[0;32m   1207\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_limit:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1100\u001b[0m, in \u001b[0;36mCursor._send_message\u001b[1;34m(self, operation)\u001b[0m\n\u001b[0;32m   1097\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexhaust cursors do not support auto encryption\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1099\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1100\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_operation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1101\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_unpack_response\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_address\u001b[49m\n\u001b[0;32m   1102\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1103\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m OperationFailure \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m   1104\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mcode \u001b[38;5;129;01min\u001b[39;00m _CURSOR_CLOSED_ERRORS \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust:\n\u001b[0;32m   1105\u001b[0m         \u001b[38;5;66;03m# Don't send killCursors because the cursor is already closed.\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[0;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1752\u001b[0m, in \u001b[0;36mMongoClient._run_operation\u001b[1;34m(self, operation, unpack_res, address)\u001b[0m\n\u001b[0;32m   1742\u001b[0m     operation\u001b[38;5;241m.\u001b[39mreset()  \u001b[38;5;66;03m# Reset op in case of retry.\u001b[39;00m\n\u001b[0;32m   1743\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\u001b[38;5;241m.\u001b[39mrun_operation(\n\u001b[0;32m   1744\u001b[0m         conn,\n\u001b[0;32m   1745\u001b[0m         operation,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1749\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1750\u001b[0m     )\n\u001b[1;32m-> 1752\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retryable_read\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1753\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_cmd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1754\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_preference\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1755\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[0;32m   1756\u001b[0m \u001b[43m    \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1757\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_Query\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1758\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1759\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1861\u001b[0m, in \u001b[0;36mMongoClient._retryable_read\u001b[1;34m(self, func, read_pref, session, operation, address, retryable, operation_id)\u001b[0m\n\u001b[0;32m   1856\u001b[0m \u001b[38;5;66;03m# Ensure that the client supports retrying on reads and there is no session in\u001b[39;00m\n\u001b[0;32m   1857\u001b[0m \u001b[38;5;66;03m# transaction, otherwise, we will not support retry behavior for this call.\u001b[39;00m\n\u001b[0;32m   1858\u001b[0m retryable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mbool\u001b[39m(\n\u001b[0;32m   1859\u001b[0m     retryable \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mretry_reads \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (session \u001b[38;5;129;01mand\u001b[39;00m session\u001b[38;5;241m.\u001b[39min_transaction)\n\u001b[0;32m   1860\u001b[0m )\n\u001b[1;32m-> 1861\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retry_internal\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1862\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1863\u001b[0m \u001b[43m    \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1864\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1865\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1866\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_read\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1867\u001b[0m \u001b[43m    \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1868\u001b[0m \u001b[43m    \u001b[49m\u001b[43mread_pref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_pref\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1869\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1870\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1871\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[0;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1828\u001b[0m, in \u001b[0;36mMongoClient._retry_internal\u001b[1;34m(self, func, session, bulk, operation, is_read, address, read_pref, retryable, operation_id)\u001b[0m\n\u001b[0;32m   1791\u001b[0m \u001b[38;5;129m@_csot\u001b[39m\u001b[38;5;241m.\u001b[39mapply\n\u001b[0;32m   1792\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_retry_internal\u001b[39m(\n\u001b[0;32m   1793\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1802\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[0;32m   1803\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m   1804\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Internal retryable helper for all client transactions.\u001b[39;00m\n\u001b[0;32m   1805\u001b[0m \n\u001b[0;32m   1806\u001b[0m \u001b[38;5;124;03m    :param func: Callback function we want to retry\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1815\u001b[0m \u001b[38;5;124;03m    :return: Output of the calling func()\u001b[39;00m\n\u001b[0;32m   1816\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m   1817\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ClientConnectionRetryable\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1818\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmongo_client\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1819\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfunc\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1820\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbulk\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbulk\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1821\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1822\u001b[0m \u001b[43m        \u001b[49m\u001b[43mis_read\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_read\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1823\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1824\u001b[0m \u001b[43m        \u001b[49m\u001b[43mread_pref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_pref\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1825\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1826\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1827\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m-> 1828\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2565\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable.run\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2563\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error(check_csot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m   2564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 2565\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_read \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write()\n\u001b[0;32m   2566\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ServerSelectionTimeoutError:\n\u001b[0;32m   2567\u001b[0m     \u001b[38;5;66;03m# The application may think the write was never attempted\u001b[39;00m\n\u001b[0;32m   2568\u001b[0m     \u001b[38;5;66;03m# if we raise ServerSelectionTimeoutError on the retry\u001b[39;00m\n\u001b[0;32m   2569\u001b[0m     \u001b[38;5;66;03m# attempt. Raise the original exception instead.\u001b[39;00m\n\u001b[0;32m   2570\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error()\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2700\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._read\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2695\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_read\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m   2696\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Wrapper method for read-type retryable client executions\u001b[39;00m\n\u001b[0;32m   2697\u001b[0m \n\u001b[0;32m   2698\u001b[0m \u001b[38;5;124;03m    :return: Output for func()'s call\u001b[39;00m\n\u001b[0;32m   2699\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 2700\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_server\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2701\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRead Preference required on read calls\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   2702\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39m_conn_from_server(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_session) \u001b[38;5;28;01mas\u001b[39;00m (\n\u001b[0;32m   2703\u001b[0m         conn,\n\u001b[0;32m   2704\u001b[0m         read_pref,\n\u001b[0;32m   2705\u001b[0m     ):\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2656\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._get_server\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2651\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_get_server\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[0;32m   2652\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Retrieves a server object based on provided object context\u001b[39;00m\n\u001b[0;32m   2653\u001b[0m \n\u001b[0;32m   2654\u001b[0m \u001b[38;5;124;03m    :return: Abstraction to connect to server\u001b[39;00m\n\u001b[0;32m   2655\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 2656\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2657\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_server_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2658\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2659\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_operation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2660\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2661\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_deprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2662\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_operation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2663\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1647\u001b[0m, in \u001b[0;36mMongoClient._select_server\u001b[1;34m(self, server_selector, session, operation, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m   1645\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m AutoReconnect(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mserver \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m no longer available\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m address)  \u001b[38;5;66;03m# noqa: UP031\u001b[39;00m\n\u001b[0;32m   1646\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1647\u001b[0m         server \u001b[38;5;241m=\u001b[39m \u001b[43mtopology\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1648\u001b[0m \u001b[43m            \u001b[49m\u001b[43mserver_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1649\u001b[0m \u001b[43m            \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1650\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1651\u001b[0m \u001b[43m            \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1652\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\n\u001b[0;32m   1654\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m PyMongoError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# Server selection errors in a transaction are transient.\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:402\u001b[0m, in \u001b[0;36mTopology.select_server\u001b[1;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m    392\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mselect_server\u001b[39m(\n\u001b[0;32m    393\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    394\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    399\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    400\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[0;32m    401\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Like select_servers, but choose a random server if several match.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 402\u001b[0m     server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    403\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    404\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    405\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_selection_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    406\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    407\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    408\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    409\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    410\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _csot\u001b[38;5;241m.\u001b[39mget_timeout():\n\u001b[0;32m    411\u001b[0m         _csot\u001b[38;5;241m.\u001b[39mset_rtt(server\u001b[38;5;241m.\u001b[39mdescription\u001b[38;5;241m.\u001b[39mmin_round_trip_time)\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:380\u001b[0m, in \u001b[0;36mTopology._select_server\u001b[1;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_select_server\u001b[39m(\n\u001b[0;32m    372\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    373\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    378\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    379\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[1;32m--> 380\u001b[0m     servers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect_servers\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    381\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_selection_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_id\u001b[49m\n\u001b[0;32m    382\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    383\u001b[0m     servers \u001b[38;5;241m=\u001b[39m _filter_servers(servers, deprioritized_servers)\n\u001b[0;32m    384\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(servers) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:287\u001b[0m, in \u001b[0;36mTopology.select_servers\u001b[1;34m(self, selector, operation, server_selection_timeout, address, operation_id)\u001b[0m\n\u001b[0;32m    284\u001b[0m     server_timeout \u001b[38;5;241m=\u001b[39m server_selection_timeout\n\u001b[0;32m    286\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n\u001b[1;32m--> 287\u001b[0m     server_descriptions \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_servers_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    288\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\n\u001b[0;32m    289\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    291\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [\n\u001b[0;32m    292\u001b[0m         cast(Server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_server_by_address(sd\u001b[38;5;241m.\u001b[39maddress)) \u001b[38;5;28;01mfor\u001b[39;00m sd \u001b[38;5;129;01min\u001b[39;00m server_descriptions\n\u001b[0;32m    293\u001b[0m     ]\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:354\u001b[0m, in \u001b[0;36mTopology._select_servers_loop\u001b[1;34m(self, selector, timeout, operation, operation_id, address)\u001b[0m\n\u001b[0;32m    342\u001b[0m     _debug_log(\n\u001b[0;32m    343\u001b[0m         _SERVER_SELECTION_LOGGER,\n\u001b[0;32m    344\u001b[0m         message\u001b[38;5;241m=\u001b[39m_ServerSelectionStatusMessage\u001b[38;5;241m.\u001b[39mWAITING,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    350\u001b[0m         remainingTimeMS\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mint\u001b[39m(end_time \u001b[38;5;241m-\u001b[39m time\u001b[38;5;241m.\u001b[39mmonotonic()),\n\u001b[0;32m    351\u001b[0m     )\n\u001b[0;32m    352\u001b[0m     logged_waiting \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 354\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_ensure_opened\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    355\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request_check_all()\n\u001b[0;32m    357\u001b[0m \u001b[38;5;66;03m# Release the lock and wait for the topology description to\u001b[39;00m\n\u001b[0;32m    358\u001b[0m \u001b[38;5;66;03m# change, or for a timeout. We won't miss any changes that\u001b[39;00m\n\u001b[0;32m    359\u001b[0m \u001b[38;5;66;03m# came after our most recent apply_selector call, since we've\u001b[39;00m\n\u001b[0;32m    360\u001b[0m \u001b[38;5;66;03m# held the lock until now.\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:778\u001b[0m, in \u001b[0;36mTopology._ensure_opened\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    773\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Start monitors, or restart after a fork.\u001b[39;00m\n\u001b[0;32m    774\u001b[0m \n\u001b[0;32m    775\u001b[0m \u001b[38;5;124;03mHold the lock when calling this.\u001b[39;00m\n\u001b[0;32m    776\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    777\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_closed:\n\u001b[1;32m--> 778\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCannot use MongoC<PERSON> after close\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    780\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_opened:\n\u001b[0;32m    781\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_opened \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[1;31mInvalidOperation\u001b[0m: Cannot use MongoClient after close"]}], "source": ["def find_one_document(collection, query={}):\n", "    return collection.find_one(query)\n", "\n", "# Example usage\n", "db = get_database()\n", "query = {\"name\": \"<PERSON>\"}\n", "result = find_one_document(collection, query)\n", "print(\"Found Document:\", result)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found Document: None\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "def get_database():\n", "    client = MongoClient(\"mongodb://localhost:27017/\")  # Ensure the connection is open\n", "    return client, client[\"test_database\"]  # Return both client and database\n", "\n", "def find_one_document(collection, query={}):\n", "    return collection.find_one(query)\n", "\n", "# Example usage\n", "client, db = get_database()  # Keep client open\n", "collection = db[\"test_collection\"]\n", "\n", "query = {\"name\": \"<PERSON>\"}\n", "result = find_one_document(collection, query)\n", "\n", "print(\"Found Document:\", result)\n", "\n", "# Close the connection at the end of the script\n", "client.close()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'items'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 12\u001b[0m\n\u001b[0;32m      9\u001b[0m sample_doc \u001b[38;5;241m=\u001b[39m collection\u001b[38;5;241m.\u001b[39mfind_one()\n\u001b[0;32m     11\u001b[0m \u001b[38;5;66;03m# Extract schema (field names and types)\u001b[39;00m\n\u001b[1;32m---> 12\u001b[0m schema \u001b[38;5;241m=\u001b[39m {key: \u001b[38;5;28mtype\u001b[39m(value)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m key, value \u001b[38;5;129;01min\u001b[39;00m \u001b[43msample_doc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m()}\n\u001b[0;32m     14\u001b[0m \u001b[38;5;66;03m# Print schema\u001b[39;00m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSchema:\u001b[39m\u001b[38;5;124m\"\u001b[39m, schema)\n", "\u001b[1;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'items'"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://localhost:27017/\")\n", "db = client[\"my_database\"]\n", "collection = db[\"my_collection\"]\n", "\n", "# Get a sample document\n", "sample_doc = collection.find_one()\n", "\n", "# Extract schema (field names and types)\n", "schema = {key: type(value).__name__ for key, value in sample_doc.items()}\n", "\n", "# Print schema\n", "print(\"Schema:\", schema)\n", "\n", "# Close connection\n", "client.close()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["sample_doc"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique field names in the collection:\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Replace the URI, database name, and collection name as needed\n", "mongo_uri = \"mongodb://localhost:27017\"\n", "database_name = \"your_database\"\n", "collection_name = \"your_collection\"\n", "\n", "# Create a MongoClient and connect to the database\n", "client = MongoClient(mongo_uri)\n", "db = client[database_name]\n", "collection = db[collection_name]\n", "\n", "# Define the aggregation pipeline:\n", "# 1. $project: Convert each document to an array of key-value pairs.\n", "# 2. $unwind: Deconstruct the array so each element (i.e. key-value pair) becomes a separate document.\n", "# 3. $group: Group by the key name (_id) to get a unique list of field names.\n", "pipeline = [\n", "    {\n", "        \"$project\": {\n", "            \"keys\": {\"$objectToArray\": \"$$ROOT\"}\n", "        }\n", "    },\n", "    {\n", "        \"$unwind\": \"$keys\"\n", "    },\n", "    {\n", "        \"$group\": {\n", "            \"_id\": \"$keys.k\"\n", "        }\n", "    }\n", "]\n", "\n", "# Execute the aggregation pipeline\n", "schema_fields = collection.aggregate(pipeline)\n", "\n", "# Print out the unique field names (schema keys)\n", "print(\"Unique field names in the collection:\")\n", "for field in schema_fields:\n", "    print(field[\"_id\"])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run Query"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'_id': ObjectId('67b88b42b50a0626e3608ef2'), 'Resume': {'PersonalInformation': {'FullName': 'More Tanajee Sunil', 'Gender': 'Male', 'BirthDate': 'May 10th, 2001', 'Email': '<EMAIL>', 'Address': 'SRUSHTI BUNGLOWS, Thaltej, Ahmedabad, Gujarat 380060', 'ContactNumber': '9763900816', 'LinkedInProfile': 'https://www.linkedin.com/in/tanajee-more-1002b6258'}, 'Objective': 'To seek a place where i can use my Knowlegde, upgrade my skill and contribute to the organization with my full potential', 'Education': [{'Degree': 'B-Pharmacy', 'Institution': 'Kishori Collage of B-pharmacy Telgaon-naka Pimpelner Road Beed.,', 'GraduationYear': '2019 2023 ', 'GPA/Marks/%': '7.72 CGPA'}, {'Degree': 'HSC', 'Institution': '<PERSON> Junior Collage Prabhupimpri Pathardi', 'GraduationYear': '2019 ', 'GPA/Marks/%': '61.23%'}, {'Degree': 'SSC', 'Institution': 'Sanskar Vidyalaya New Bhaji Mandai Beed.,', 'GraduationYear': '2017 ', 'GPA/Marks/%': '80.20%'}], 'WorkExperience': [{'CompanyName': 'Limpid Global Solution', 'Role': 'Medical Coder Trainee', 'StartYear': '', 'EndYear': 'December 2023   present ', 'Description/Responsibility': \"A Medical Coder transcribes patients' records into an insurance-accepted coding system. With this information, they are able to process claims for reimbursement from providers in accordance with agreements.\"}], 'Skills': ['Medical coding,Medical Billing,Payment Posting,Human Anatomy & Physiology,Medical Terminology,MS-Office,ICH guidelines, Pharmacology,Good Clinical Practices\\nMicrosoft PowerPoint,Microsoft Excel,English Typing (30 WPM 40 WPM),Steno English Multitasking,Team work,Time Management,Social Media'], 'Certifications': [{'CertificationName': 'POSHAN Abhiyan E-Learning ICMR Modules', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': 'none'}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': 'g', 'IssueDate': ''}], 'Languages': [''], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608ef6'), 'Resume': {'PersonalInformation': {'FullName': 'AMMAR MANSURI', 'Gender': 'Male', 'BirthDate': '03/02/1194', 'Email': '<EMAIL>', 'Address': '3296,Nani saleperi opp police\\n tambuchowki,Dariyapuri,Ahmedabad(Gujarat)380001', 'ContactNumber': '**********', 'LinkedInProfile': ''}, 'Objective': \"Seeking full-time position in finance an innovation company that foster\\n an enviroment of creativity continual learning among it's employees\\n I am aspiring for a career opportunities whwer I can significantly\\n contribute to the growth of the organization\", 'Education': [{'Degree': '10th', 'Institution': 'F.D High School', 'GraduationYear': '', 'GPA/Marks/%': '57%'}, {'Degree': '12th', 'Institution': 'F.D High School', 'GraduationYear': '', 'GPA/Marks/%': '47%'}, {'Degree': 'BACHELOR OF COMMERCE', 'Institution': 'Gujarat University', 'GraduationYear': '2016', 'GPA/Marks/%': ''}], 'WorkExperience': [{'CompanyName': 'KGN IT SOLUTIONS PVT LTD.', 'Role': 'ACCOUNTANT', 'StartYear': '2017', 'EndYear': \"'Present'\", 'Description/Responsibility': ''}], 'Skills': ['Tally ERP 9/Tally Prime\\nMs office(Excel,word)\\nGST\\nTDS\\nDaily cash &Bank receipt & payment details\\n Sales& Purchase invoice process\\nVender Reconciliation\\nSalary process\\nPreparing files and report related to month end activity'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': [''], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608efc'), 'Resume': {'PersonalInformation': {'FullName': '<PERSON><PERSON>', 'Gender': 'Male', 'BirthDate': '08/31/95', 'Email': '<EMAIL>', 'Address': 'Udaipur, Rajasthan 313001 ', 'ContactNumber': '91-********** ', 'LinkedInProfile': ''}, 'Objective': 'Versatile Accounting Professional, Expert in Analytics, Supervision, and    Team Leadership. Dedicated professional with a strong desire to  leverage expertise in accounting to meet organizational objectives. ', 'Education': [{'Degree': 'Chartered Accountant: Accounting And Finance  ', 'Institution': 'ICAI Udaipur', 'GraduationYear': 'May 2018', 'GPA/Marks/%': ''}, {'Degree': 'Company Secretary: Accounting and Finance ', 'Institution': 'ICSI Udaipur', 'GraduationYear': 'June 2019', 'GPA/Marks/%': ' 60%'}, {'Degree': 'Master of Commerce: Accounting and Finance ', 'Institution': 'MLSU Udaipur', 'GraduationYear': ' June 2017', 'GPA/Marks/%': '69.70%'}, {'Degree': 'Bachelor of Commerce: Accounting and Finance ', 'Institution': 'MLSU  Udaipur', 'GraduationYear': 'June 2015', 'GPA/Marks/%': '69.70%'}, {'Degree': 'Higher Secondary Examinations: Accounting And  Business Management  ', 'Institution': 'ICAI Udaipur', 'GraduationYear': 'May 2012 ', 'GPA/Marks/%': ': 70.81%'}, {'Degree': 'Senior Secondary Examinations: General Studies  ', 'Institution': 'RBSE  Udaipur', 'GraduationYear': 'May 2010', 'GPA/Marks/%': '79.50%'}], 'WorkExperience': [{'CompanyName': 'Analytix Business Solutions (I) Pvt. Ltd', 'Role': 'Senior Team Leader ', 'StartYear': '', 'EndYear': 'Dec 2020 - Currently working ', 'Description/Responsibility': 'Prepared and finalized financial data for end \\nclients and CPA partners./n Set clear team goals and delegated tasks and \\n set deadlines. \\n Conducted bookkeeping, month-end closing, \\n and financial review using Quick Books, Zoho \\n books, Xero, Bill.com etc. \\nHandled state sales tax filings for clients. \\nManaged preparation and filing of Form 1099.\\n Processed payroll through Gusto, ADP, Paychex \\n software, and conducted periodical \\nreconciliations.\\n Utilized inventory management systems to track \\n   and restock supplies such as Dear Inventory\\n  System (Cin7 Core) and MS Excel. \\n  Oversaw clients with E-commerce sales through \\n  Shopify, Stripe, Amazon, and eBay. \\n  Interacted with CPA firms and directly with clients \\n to address financial matters. \\n   Successfully managed and led team of 12-14\\n   team members.\\n   Discovered training needs and provided \\n   coaching.\\n   Established open and professional relationships\\n  with team members to achieve quick resolutions    for various issues.\\n  Managed leave requests, absences and \\n    arranged covers to facilitate smooth flow of \\n   operations. \\n  '}, {'CompanyName': 'RJKPJ & Associates ', 'Role': 'Accounts Executive ', 'StartYear': 'Aug 2018', 'EndYear': 'Dec 2020 ', 'Description/Responsibility': ' Preparation of Periodical VAT and GST Returns  \\n    Preparation for Financial statements of \\n   Individuals, Firm and Companies \\n   Stock Audit and Revenue Audit of various \\n   Borrowers of Bank  \\n   Analyzed accounts for delinquencies and other \\n  ongoing issues.'}, {'CompanyName': 'M/S L. S. Nalwaya & Co., Udaipur ', 'Role': 'Intern', 'StartYear': 'Feb 2014 ', 'EndYear': 'Feb 2017', 'Description/Responsibility': 'Statutory & Tax Audits of various Companies,Partnership Firms, Trust & Individuals, /n Managed over 100 clients for Sales tax and  Income Tax related workStatutory Compliances like Income Tax Returns, VAT, TDS Returns and ROC fillings.'}], 'Skills': ['Strategic planning,  Retail inventory Management, Key performance indicators ,Excellent attention to Details,Fast learner &  Key Team Player ,Delegation'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': ''}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': ['English,Hindi'], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608efe'), 'Resume': {'PersonalInformation': {'FullName': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ya ', 'Gender': 'Male', 'BirthDate': '29thMarch1998', 'Email': '<EMAIL>', 'Address': 'ActualFullAddressInString', 'ContactNumber': '7202909521', 'LinkedInProfile': ''}, 'Objective': 'To be a part of an organization where I can apply and enhance my technical skills and\\n  knowledge. I would like to do work which can provide me an insight into new aspects so \\nthat it would be helpful for professional and personal growth. ', 'Education': [{'Degree': 'HSC', 'Institution': 'Akshay High School', 'GraduationYear': '2015', 'GPA/Marks/%': '70%'}, {'Degree': '(B.Com)', 'Institution': 'GLS University', 'GraduationYear': '2019', 'GPA/Marks/%': ''}, {'Degree': ' (M.Com) ', 'Institution': 'Gujarat University. ', 'GraduationYear': '2021', 'GPA/Marks/%': ''}], 'WorkExperience': [{'CompanyName': 'Neuberg Diagnostics Private Limited. ', 'Role': 'Senior Account Executive', 'StartYear': 'Sep-2019', 'EndYear': \"'Present'\", 'Description/Responsibility': 'Preparing MIS reports of Sister-concerns.\\n Procurement Booking, Payment Control and Coordination with Vendors \\n for ledger reconciliations.\\n   Preparation of Bank Reconciliation, TDS Reconciliation.\\n Preparations of TDS/TCS working and Payment there of. \\n  Controlling the day-to-day cash collections and co-ordination thereof at various\\n  locations.\\n   Bookkeeping of Sister-concerns in tally up to finalization of accounts and audit.\\n  Preparations of Monthly and Weekly reports from books of accounts as required \\n by management .\\n  Comprehensive knowledge of procedures related to import payments.\\n   Work in SAP (S4 Hana) and Tally Prime.\\n  '}, {'CompanyName': 'CSR Digital & Smart\\n Education Centre', 'Role': 'professor', 'StartYear': 'June-2017', 'EndYear': \"'June-2018'\", 'Description/Responsibility': 'Teaching Tally ERP.9 with GST. \\n Teaching CCC course.  '}, {'CompanyName': 'Bhadreswar Traders &\\nChellenj Marketing.  ', 'Role': 'accountant', 'StartYear': 'July-2018', 'EndYear': \"'Sep-2019'\", 'Description/Responsibility': 'Preparing a invoice of Sales & Purchases.\\n Preparing of Bank Reconciliation. \\nControlling day-to-day cash collections.'}], 'Skills': ['Microsoft Advance Excel Functions (365)\\n Microsoft Power BI (Business Intelligence) \\nMicrosoft Power Query \\nAutomation - Experience with automating repetitive tasks and processes using\\n AI tools and software, such as robotic process automation (RPA). '], 'Certifications': [{'CertificationName': ' Computer Concept (CCC)', 'IssuingOrganization': 'y HI-TECH Education Class', 'IssueDate': '', 'ExpiryDate': \"'None'\"}, {'CertificationName': 'Tally ERP.9 ', 'IssuingOrganization': 'HI-TECH Education Class', 'IssueDate': '', 'ExpiryDate': \"'None'\"}, {'CertificationName': ' Advance Excel Functions', 'IssuingOrganization': 'Microsoft', 'IssueDate': '', 'ExpiryDate': \"'None'\"}, {'CertificationName': ' Power BI', 'IssuingOrganization': 'Microsoft', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': [' English, Gujarati & Hindi. '], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608f12'), 'Resume': {'PersonalInformation': {'FullName': 'Parth Varmora', 'Gender': 'Male', 'BirthDate': '10th March, 2001  ', 'Email': '<EMAIL>', 'Address': 'Ahmedabad', 'ContactNumber': '9737528295', 'LinkedInProfile': ''}, 'Objective': '1. Obtain recognition in an organization providing opportunities.\\n 2. An opportunity to join a progressive organization where I can use my skills to make a\\n significant contribution to the success of the company. ', 'Education': [{'Degree': 'US CPA', 'Institution': '', 'GraduationYear': 'Pursuing', 'GPA/Marks/%': ''}, {'Degree': 'M.COM', 'Institution': 'GUJARAT \\nUNIVERSITY', 'GraduationYear': '2023', 'GPA/Marks/%': '68.2% '}, {'Degree': 'B.COM', 'Institution': 'GUJARAT \\nUNIVERSITY', 'GraduationYear': '2021', 'GPA/Marks/%': '76.40%'}, {'Degree': 'H.S.C', 'Institution': 'G.S.H.S.E.B', 'GraduationYear': '2018', 'GPA/Marks/%': '79.73%'}, {'Degree': 'S.S.C', 'Institution': 'G.S.H.S.E.B', 'GraduationYear': '2016', 'GPA/Marks/%': '51.14%'}], 'WorkExperience': [{'CompanyName': 'KPJ Tax\\nMasters, LLP', 'Role': 'Tax staff', 'StartYear': '', 'EndYear': '', 'Description/Responsibility': ''}], 'Skills': ['Communication » Critical thinking » Leadership +Management» Teamwork« Problem Solving » Leadership'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': ['Gujarati, Hindi, English'], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608f18'), 'Resume': {'PersonalInformation': {'FullName': 'CA Hardik M. <PERSON>bha<PERSON>', 'Gender': 'Male', 'BirthDate': 'May 17, 1994 ', 'Email': '<EMAIL>', 'Address': 'C-203 Aditi Emperia, \\nNr.Vishwkarma Temple, \\nNew SG Road, Chandlodiya\\n Ahmedabad - 382481 ', 'ContactNumber': '+91 99988 28317 / 88668 79903 ', 'LinkedInProfile': ''}, 'Objective': 'To initiate my professional carrier being associated with accounting, finance & business development areas of an \\nentity which has its existence across the Nation as well as International Level. ', 'Education': [{'Degree': 'CA FINAL', 'Institution': 'ICAI', 'GraduationYear': 'May 2019', 'GPA/Marks/%': ''}, {'Degree': 'CA IPCC', 'Institution': 'ICAI', 'GraduationYear': 'Nov 2013', 'GPA/Marks/%': ''}, {'Degree': 'CA CPT', 'Institution': 'ICAI', 'GraduationYear': 'June 2012', 'GPA/Marks/%': ''}, {'Degree': 'B. Com ', 'Institution': 'Gujarat University', 'GraduationYear': 'July 2015', 'GPA/Marks/%': ''}], 'WorkExperience': [{'CompanyName': 'Rinkesh Shah & Co. (CA Firm)', 'Role': 'Senior Operational Manager (Accounts & Finance) ', 'StartYear': 'April 2017', 'EndYear': 'August 2019 ', 'Description/Responsibility': 'Statutory Bank Audit:\\n\\uf0b7 Involved in statutory audit work of various rural branch of State Bank of India & Union Bank \\nof India. \\ne Sector Companies:\\n\\uf0b7 Engaged in work of statutory audit, Accounting, Direct & Indirect Taxation work other \\ny work of many private companies. \\ns: \\n\\uf0b7 Preparation of Company Accounts (Domestic) \\n\\uf0b7 Verification of purchase bill, Cash payment Vouchers. \\n\\uf0b7 Checking of debtors and creditors outstanding statements monthly. \\n\\uf0b7 Preparation of Bank reconciliation statement.\\n \\uf0b7 Finalization of Accounts. \\ng: \\n\\uf0b7 Attending to various types of bank audits namely:- \\n\\uf0fc Statutory Audit \\n\\uf0fc Stock Audit \\n\\uf0fc Concurrent Audit \\n\\uf0fc Tax Audit \\nSmall Business Funding:\\n\\uf0b7 Preparations of working capital forecast for clients. \\n\\uf0b7 Submission of stock statements for clients. \\nIncome Tax: \\n\\uf0b7 Preparation and filing of returns of individuals, partnerships, and Companies. \\n\\uf0b7 Applicability of TDS from different types of works. \\n\\uf0b7 Tax planning for clients. \\n\\uf0b7 Handling tax audits of various clients including private limited companies, and \\nfirms'}, {'CompanyName': 'Sustainable BPO LLP (BPO run by Grant Thornton Uganda) ', 'Role': 'Assistant Executive', 'StartYear': 'September 2019', 'EndYear': 'August 2020 ', 'Description/Responsibility': 'Responsible for overall client portfolio \\n\\uf0b7 Review work including \\n\\uf0fc Bookkeeping done by in charge person \\n\\uf0fc Necessary adjustment for month-end and year-end \\n\\uf0fc Monthly Reports prepared by in charge \\n\\uf0b7 Liaising with \\n\\uf0fc Client for understanding their business and reporting requirement \\n\\uf0fc Auditor for any matters related to compliance and reporting \\n\\uf0fc Tax Authorities for any queries related to Tax Compliance \\n'}, {'CompanyName': 'QX Global Services LLP ', 'Role': 'Senior Accounts Officer (Management Accountant', 'StartYear': 'September 2020', 'EndYear': 'March 2020 ', 'Description/Responsibility': '\\uf0b7 Preliminary Month-End Activities – \\n\\uf0b7 Management Accounts Preparation \\n\\uf0b7 Scrutiny of expenses and balances of real accounts and posting of adjustments if required \\n\\uf0b7 Analyzing the variances and reasoning behind variances \\n\\uf0b7 Reverting client’s review points \\n\\uf0b7 Balance Sheet Reconciliation \\n          \\uf0b7 Monthly Property Maintenance Report \\n\\uf0b7 VAT Return preparation \\n\\uf0b7 Cash flow statement \\n\\uf0b7 Client payment statement \\n\\uf0b7 Intercompany reconciliation \\n\\uf0b7 Resolving queries and assisting the auditor \\n'}, {'CompanyName': 'GIRI Hotels Management India Pvt. Ltd. (Subsidiary of GIRI Hotels Management \\nChain based in the U.S.A) ', 'Role': 'Senior Accountant ', 'StartYear': 'April 2022', 'EndYear': 'June 2023', 'Description/Responsibility': \"Responsible for handling overall bookkeeping & reporting to owners for 14 out of 50 properties under \\nl branding like Hilton, Choice, Wyndham, etc. which includes the following: - \\n\\uf0b7 Review of end-to-end bookkeeping done by Hotel Management System (PMS) \\n\\uf0b7 Review Payroll entries that are populated via Inova payroll software. \\n\\uf0b7 Review and balance Daily hotel activity generated via the interface between accounting software and the \\n          hotel PMS system. \\n\\uf0b7 Reconciliation of bank accounts & Credit card statements & daily cash collection. \\n\\uf0b7 Making adjusting and accrual/prepaid entries as necessary at the month's end. \\n\\uf0b7 Review P&L by comparing the budget and prior period to identify expenses over budget, unusual activity, and \\nduplicates. \\n\\uf0b7 Assist in End of Year Audit Preparations. \\n\\uf0b7 Reporting and other tasks as needed and assigned such as sales tax return filing, and MIS preparation for \\nproperty owners. \\n\\uf0b7 Liaison with the hotel general manager in case of any disconnect regarding daily operations. \"}, {'CompanyName': 'Rubino & Co., Chartered - CPA ', 'Role': 'Senior Accountant ', 'StartYear': 'July 2022', 'EndYear': 'Present  ', 'Description/Responsibility': '\\uf0b7 Preparing day-to-day and month-end close entries for revenue, payroll, prepaid expenses, and accrued \\ns. \\n\\uf0b7 Preparing monthly reconciliations for balance sheet accounts, payroll expenses, and other general ledger \\naccounts. \\n\\uf0b7 Ensuring internal control processes specific to each assigned client. \\n\\uf0b7 Prepare audit and tax schedules at year-end. \\n\\uf0b7 Assist in the preparation of year-end 1099 submissions. \\n\\uf0b7 Design and implement controls and procedures. \\n\\uf0b7 Interact with clients on a regular basis. '}], 'Skills': ['Exposure of Accounting software such as \\no Tally ERP \\no M3 Accounting Core – Specifically designed to be used in Hospitality industries \\no QuickBooks Desktop \\n\\uf0b7 Exposure of MS Office tools based on needs \\no MS Excel – working knowledge \\n\\uf0b7 Analytical skills involving Comparison of historical data using MS Excel \\n'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '\\uf0b7 Secured 78 Marks in Strategic Financial Management in CA Final which is one of the \\nCritical subject for CA Final students', 'IssuingOrganization': '', 'IssueDate': ''}, {'AchievementName': 'Secured 63 marks in Information System Control Audit in CA Final which one also the \\nCritical & deciding subject for CA Final students', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': ['Gujarati, Hindi & English '], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608f1e'), 'Resume': {'PersonalInformation': {'FullName': '<PERSON><PERSON>', 'Gender': 'Male', 'BirthDate': '08/05/1993', 'Email': '<EMAIL>', 'Address': 'B-23 Sundaram Avenue\\nOpp shriram Residency\\nNarol, Ahmedabad\\n5', 'ContactNumber': '+91-7069613263, 8849483126', 'LinkedInProfile': ''}, 'Objective': 'To work in an organization where I can acquire new knowledge and sharpen\\nmyskills', 'Education': [{'Degree': '10th', 'Institution': 'Gujarat Board', 'GraduationYear': '2010', 'GPA/Marks/%': '44.29%'}, {'Degree': '12th', 'Institution': 'Gujarat Board', 'GraduationYear': '2012', 'GPA/Marks/%': '54.29%'}, {'Degree': 'B.com', 'Institution': 'Gujarat University', 'GraduationYear': '2016', 'GPA/Marks/%': '47.57%'}, {'Degree': 'M.com', 'Institution': 'Gujarat University', 'GraduationYear': '2018', 'GPA/Marks/%': '46%'}], 'WorkExperience': [{'CompanyName': 'Riddhi Steel Pvt. Ltd', 'Role': ' Jr. Accountant', 'StartYear': '', 'EndYear': \"  ''\", 'Description/Responsibility': ''}], 'Skills': ['Can work under any circumstances\\n\\uf0f0 Good listener - caring and compassionate.\\n\\uf0f0 Handling Cash & Expense Vouchers Entry.\\n\\uf0f0 Excellent interpersonal skills - good communicator, leadership, high integrity, work\\nwell with others, motivate and encourages'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': ['Hindi, Gujarati & English'], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608f20'), 'Resume': {'PersonalInformation': {'FullName': 'Jain <PERSON>', 'Gender': 'Male', 'BirthDate': '12-04-2004', 'Email': '<EMAIL>', 'Address': '1/13 Shubh Laxmi flat NR.D/19 Hospital, Bapunagar\\nAhmedabad, Gujarat, 380034', 'ContactNumber': '917600601771', 'LinkedInProfile': ''}, 'Objective': 'To work and succeed in a stimulating and challenging environment, building the success of the company\\ne I experience advancement opportunities.', 'Education': [{'Degree': 'SSC', 'Institution': 'SARVODAY\\nVIDHYAMANDIR', 'GraduationYear': '2021', 'GPA/Marks/%': '93 %'}, {'Degree': 'HSC', 'Institution': '<PERSON><PERSON><PERSON><PERSON>', 'GraduationYear': '2023', 'GPA/Marks/%': '85 %'}], 'WorkExperience': [{'CompanyName': '', 'Role': 'FRESHER', 'StartYear': '', 'EndYear': '', 'Description/Responsibility': ''}], 'Skills': ['Computer Basic\\nKnowledge'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': ['Hindi, Gujrati English'], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n", "{'_id': ObjectId('67b88b42b50a0626e3608f2c'), 'Resume': {'PersonalInformation': {'FullName': '<PERSON><PERSON><PERSON>tal<PERSON>', 'Gender': 'Male', 'BirthDate': '04 May 2000', 'Email': '<EMAIL>', 'Address': 'Dhoraji, Gujarat ', 'ContactNumber': '6355451926', 'LinkedInProfile': ''}, 'Objective': 'To work with an organization that provide me challenging opportunities for enhancement of my skills \\nand knowledge so that I can directly contribute the efforts into a meaningful contribution towards\\norganizational goals as well as the personal goals.', 'Education': [{'Degree': 'MBA(Finance)', 'Institution': 'GTU', 'GraduationYear': 'September 2023 ', 'GPA/Marks/%': '6.80'}, {'Degree': 'B.Com', 'Institution': 'Gujarat university', 'GraduationYear': 'August 2021', 'GPA/Marks/%': '62.21'}, {'Degree': 'Class XII ', 'Institution': 'GSEB', 'GraduationYear': 'March 2018', 'GPA/Marks/%': '76.14'}, {'Degree': 'Class X', 'Institution': 'GSEB', 'GraduationYear': 'March 2016', 'GPA/Marks/%': '68.0'}], 'WorkExperience': [{'CompanyName': 'Rajan Kalyani & Company - Rajkot', 'Role': 'Article Assistant', 'StartYear': 'November 2021', 'EndYear': 'December 2023', 'Description/Responsibility': 'Basic Knowledge of GST like monthly returns\\n- Vouching and verification of Income and expenditures.\\n- Registration of Firms\\n- Assisted in preparation of tax audit \\n- Assisted in preparation of income tax return'}], 'Skills': ['Quick Learner.\\nProblem-solving ability.\\nResponsible team leader.'], 'Certifications': [{'CertificationName': '', 'IssuingOrganization': '', 'IssueDate': '', 'ExpiryDate': \"'None'\"}], 'Achievements': [{'AchievementName': '', 'IssuingOrganization': '', 'IssueDate': ''}], 'Languages': [' English, Hindi and Gujarati'], 'Projects': [{'ProjectName': '', 'Description': '', 'TechnologiesUsed': [''], 'Role': ''}]}}\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://localhost:27017/\")  # Update with your DB details if needed\n", "db = client[\"test_database\"]  # Replace with your database name\n", "collection = db[\"test_collection\"]  # Replace with your collection name\n", "\n", "# Query to find persons born in 1985\n", "query = {\n", "    \"Resume.PersonalInformation.Gender\": \"Male\"\n", "}\n", "\n", "# Fetch results\n", "results = collection.find(query)\n", "\n", "# Print results\n", "for result in results:\n", "    print(result)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["May 10th, 2001\n", "12/05/1999\n", "12/05/1999\n", "\n", "03/02/1194\n", "\n", "\n", "27/03/2000\n", "08 January, 2005 \n", "28/09/1993\n", "08/31/95\n", "30 June, 2000\n", "29thMarch1998\n", "21st Sep 1999\n", "12thNovember1991\n", "\n", "\n", "\n", "16th August 1986\n", "\n", "7th December, 1989\n", "16-01-2001\n", "\n", "\n", "ActualBirthdateInDD/MM/YYYY\n", "\n", "\n", "26/07/1998\n", "23/04/2000\n", "\n", "07/03/2001  \n", " 19th March, 1998\n", "10th March, 2001  \n", "\n", "January 3, 1994\n", "\n", "\n", "20/07/1997\n", "May 17, 1994 \n", "24 Nov, 2001\n", "30th Aug 1988\n", "\n", "\n", "07/08/2023\n", "08/05/1993\n", "14/09/1990\n", "12-04-2004\n", "\n", "ActualBirthdateInDD/MM/YYYY\n", "\n", "11 July 2000\n", "17/06/1998\n", "27th Aug 1991 \n", "\n", "19 September 2000\n", "26/04/2001\n", "\n", "\n", "04 May 2000\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://localhost:27017/\")  # Update with your DB details if needed\n", "db = client[\"test_database\"]  # Replace with your database name\n", "collection = db[\"test_collection\"]  # Replace with your collection name\n", "\n", "# Query to find persons born in 1985\n", "query = {\n", "    \"Resume.PersonalInformation.BirthDate\": {\"$exists\": True}\n", "}\n", "\n", "# Fetch results\n", "results = collection.find(query)\n", "\n", "# Print results\n", "for result in results:\n", "    print(result[\"Resume\"][\"PersonalInformation\"][\"BirthDate\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ServerSelectionTimeoutError", "evalue": "************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 67bf49ca5637ef94d4d74f30, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mServerSelectionTimeoutError\u001b[0m               Traceback (most recent call last)", "Cell \u001b[1;32mIn[2], line 15\u001b[0m\n\u001b[0;32m     12\u001b[0m results \u001b[38;5;241m=\u001b[39m collection\u001b[38;5;241m.\u001b[39mfind(query)\n\u001b[0;32m     14\u001b[0m \u001b[38;5;66;03m# Print results\u001b[39;00m\n\u001b[1;32m---> 15\u001b[0m \u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresult\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresults\u001b[49m\u001b[43m:\u001b[49m\n\u001b[0;32m     16\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mprint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mresult\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1281\u001b[0m, in \u001b[0;36mCursor.__next__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1280\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__next__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _DocumentType:\n\u001b[1;32m-> 1281\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1257\u001b[0m, in \u001b[0;36mCursor.next\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1255\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_empty:\n\u001b[0;32m   1256\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m\n\u001b[1;32m-> 1257\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m   1258\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data\u001b[38;5;241m.\u001b[39mpopleft()\n\u001b[0;32m   1259\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1205\u001b[0m, in \u001b[0;36mCursor._refresh\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1183\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\n\u001b[0;32m   1184\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPassing a \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhint\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is required when using the min/max query\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1185\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m option to ensure the query utilizes the correct index\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1186\u001b[0m         )\n\u001b[0;32m   1187\u001b[0m     q \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_class(\n\u001b[0;32m   1188\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_flags,\n\u001b[0;32m   1189\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_collection\u001b[38;5;241m.\u001b[39mdatabase\u001b[38;5;241m.\u001b[39mname,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1203\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust,\n\u001b[0;32m   1204\u001b[0m     )\n\u001b[1;32m-> 1205\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_message\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1206\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_id:  \u001b[38;5;66;03m# Get More\u001b[39;00m\n\u001b[0;32m   1207\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_limit:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\cursor.py:1100\u001b[0m, in \u001b[0;36mCursor._send_message\u001b[1;34m(self, operation)\u001b[0m\n\u001b[0;32m   1097\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexhaust cursors do not support auto encryption\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1099\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1100\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_operation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1101\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_unpack_response\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_address\u001b[49m\n\u001b[0;32m   1102\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1103\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m OperationFailure \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m   1104\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mcode \u001b[38;5;129;01min\u001b[39;00m _CURSOR_CLOSED_ERRORS \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust:\n\u001b[0;32m   1105\u001b[0m         \u001b[38;5;66;03m# Don't send killCursors because the cursor is already closed.\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[0;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1752\u001b[0m, in \u001b[0;36mMongoClient._run_operation\u001b[1;34m(self, operation, unpack_res, address)\u001b[0m\n\u001b[0;32m   1742\u001b[0m     operation\u001b[38;5;241m.\u001b[39mreset()  \u001b[38;5;66;03m# Reset op in case of retry.\u001b[39;00m\n\u001b[0;32m   1743\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\u001b[38;5;241m.\u001b[39mrun_operation(\n\u001b[0;32m   1744\u001b[0m         conn,\n\u001b[0;32m   1745\u001b[0m         operation,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1749\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1750\u001b[0m     )\n\u001b[1;32m-> 1752\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retryable_read\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1753\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_cmd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1754\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_preference\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1755\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[0;32m   1756\u001b[0m \u001b[43m    \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1757\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_Query\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1758\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1759\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1861\u001b[0m, in \u001b[0;36mMongoClient._retryable_read\u001b[1;34m(self, func, read_pref, session, operation, address, retryable, operation_id)\u001b[0m\n\u001b[0;32m   1856\u001b[0m \u001b[38;5;66;03m# Ensure that the client supports retrying on reads and there is no session in\u001b[39;00m\n\u001b[0;32m   1857\u001b[0m \u001b[38;5;66;03m# transaction, otherwise, we will not support retry behavior for this call.\u001b[39;00m\n\u001b[0;32m   1858\u001b[0m retryable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mbool\u001b[39m(\n\u001b[0;32m   1859\u001b[0m     retryable \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mretry_reads \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (session \u001b[38;5;129;01mand\u001b[39;00m session\u001b[38;5;241m.\u001b[39min_transaction)\n\u001b[0;32m   1860\u001b[0m )\n\u001b[1;32m-> 1861\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retry_internal\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1862\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1863\u001b[0m \u001b[43m    \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1864\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1865\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1866\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_read\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1867\u001b[0m \u001b[43m    \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1868\u001b[0m \u001b[43m    \u001b[49m\u001b[43mread_pref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_pref\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1869\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1870\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1871\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[0;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1828\u001b[0m, in \u001b[0;36mMongoClient._retry_internal\u001b[1;34m(self, func, session, bulk, operation, is_read, address, read_pref, retryable, operation_id)\u001b[0m\n\u001b[0;32m   1791\u001b[0m \u001b[38;5;129m@_csot\u001b[39m\u001b[38;5;241m.\u001b[39mapply\n\u001b[0;32m   1792\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_retry_internal\u001b[39m(\n\u001b[0;32m   1793\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1802\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[0;32m   1803\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m   1804\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Internal retryable helper for all client transactions.\u001b[39;00m\n\u001b[0;32m   1805\u001b[0m \n\u001b[0;32m   1806\u001b[0m \u001b[38;5;124;03m    :param func: Callback function we want to retry\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1815\u001b[0m \u001b[38;5;124;03m    :return: Output of the calling func()\u001b[39;00m\n\u001b[0;32m   1816\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m   1817\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ClientConnectionRetryable\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1818\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmongo_client\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1819\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfunc\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1820\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbulk\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbulk\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1821\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1822\u001b[0m \u001b[43m        \u001b[49m\u001b[43mis_read\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_read\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1823\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1824\u001b[0m \u001b[43m        \u001b[49m\u001b[43mread_pref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_pref\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1825\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1826\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1827\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m-> 1828\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2565\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable.run\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2563\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error(check_csot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m   2564\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 2565\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_read \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write()\n\u001b[0;32m   2566\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ServerSelectionTimeoutError:\n\u001b[0;32m   2567\u001b[0m     \u001b[38;5;66;03m# The application may think the write was never attempted\u001b[39;00m\n\u001b[0;32m   2568\u001b[0m     \u001b[38;5;66;03m# if we raise ServerSelectionTimeoutError on the retry\u001b[39;00m\n\u001b[0;32m   2569\u001b[0m     \u001b[38;5;66;03m# attempt. Raise the original exception instead.\u001b[39;00m\n\u001b[0;32m   2570\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error()\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2700\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._read\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2695\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_read\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m   2696\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Wrapper method for read-type retryable client executions\u001b[39;00m\n\u001b[0;32m   2697\u001b[0m \n\u001b[0;32m   2698\u001b[0m \u001b[38;5;124;03m    :return: Output for func()'s call\u001b[39;00m\n\u001b[0;32m   2699\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 2700\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_server\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2701\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRead Preference required on read calls\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   2702\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39m_conn_from_server(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_session) \u001b[38;5;28;01mas\u001b[39;00m (\n\u001b[0;32m   2703\u001b[0m         conn,\n\u001b[0;32m   2704\u001b[0m         read_pref,\n\u001b[0;32m   2705\u001b[0m     ):\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:2656\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._get_server\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2651\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_get_server\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[0;32m   2652\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Retrieves a server object based on provided object context\u001b[39;00m\n\u001b[0;32m   2653\u001b[0m \n\u001b[0;32m   2654\u001b[0m \u001b[38;5;124;03m    :return: Abstraction to connect to server\u001b[39;00m\n\u001b[0;32m   2655\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 2656\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2657\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_server_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2658\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2659\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_operation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2660\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2661\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_deprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2662\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_operation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   2663\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\mongo_client.py:1647\u001b[0m, in \u001b[0;36mMongoClient._select_server\u001b[1;34m(self, server_selector, session, operation, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m   1645\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m AutoReconnect(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mserver \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m no longer available\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m address)  \u001b[38;5;66;03m# noqa: UP031\u001b[39;00m\n\u001b[0;32m   1646\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1647\u001b[0m         server \u001b[38;5;241m=\u001b[39m \u001b[43mtopology\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1648\u001b[0m \u001b[43m            \u001b[49m\u001b[43mserver_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1649\u001b[0m \u001b[43m            \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1650\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1651\u001b[0m \u001b[43m            \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1652\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\n\u001b[0;32m   1654\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m PyMongoError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# Server selection errors in a transaction are transient.\u001b[39;00m\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:402\u001b[0m, in \u001b[0;36mTopology.select_server\u001b[1;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m    392\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mselect_server\u001b[39m(\n\u001b[0;32m    393\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    394\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    399\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    400\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[0;32m    401\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Like select_servers, but choose a random server if several match.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 402\u001b[0m     server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    403\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    404\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    405\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_selection_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    406\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    407\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdeprioritized_servers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    408\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    409\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    410\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _csot\u001b[38;5;241m.\u001b[39mget_timeout():\n\u001b[0;32m    411\u001b[0m         _csot\u001b[38;5;241m.\u001b[39mset_rtt(server\u001b[38;5;241m.\u001b[39mdescription\u001b[38;5;241m.\u001b[39mmin_round_trip_time)\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:380\u001b[0m, in \u001b[0;36mTopology._select_server\u001b[1;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_select_server\u001b[39m(\n\u001b[0;32m    372\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    373\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    378\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    379\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[1;32m--> 380\u001b[0m     servers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect_servers\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    381\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_selection_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_id\u001b[49m\n\u001b[0;32m    382\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    383\u001b[0m     servers \u001b[38;5;241m=\u001b[39m _filter_servers(servers, deprioritized_servers)\n\u001b[0;32m    384\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(servers) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:287\u001b[0m, in \u001b[0;36mTopology.select_servers\u001b[1;34m(self, selector, operation, server_selection_timeout, address, operation_id)\u001b[0m\n\u001b[0;32m    284\u001b[0m     server_timeout \u001b[38;5;241m=\u001b[39m server_selection_timeout\n\u001b[0;32m    286\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n\u001b[1;32m--> 287\u001b[0m     server_descriptions \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_servers_loop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    288\u001b[0m \u001b[43m        \u001b[49m\u001b[43mselector\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maddress\u001b[49m\n\u001b[0;32m    289\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    291\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [\n\u001b[0;32m    292\u001b[0m         cast(Server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_server_by_address(sd\u001b[38;5;241m.\u001b[39maddress)) \u001b[38;5;28;01mfor\u001b[39;00m sd \u001b[38;5;129;01min\u001b[39;00m server_descriptions\n\u001b[0;32m    293\u001b[0m     ]\n", "File \u001b[1;32md:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\envSqlAgent\\Lib\\site-packages\\pymongo\\synchronous\\topology.py:337\u001b[0m, in \u001b[0;36mTopology._select_servers_loop\u001b[1;34m(self, selector, timeout, operation, operation_id, address)\u001b[0m\n\u001b[0;32m    326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _SERVER_SELECTION_LOGGER\u001b[38;5;241m.\u001b[39misEnabledFor(logging\u001b[38;5;241m.\u001b[39mDEBUG):\n\u001b[0;32m    327\u001b[0m         _debug_log(\n\u001b[0;32m    328\u001b[0m             _SERVER_SELECTION_LOGGER,\n\u001b[0;32m    329\u001b[0m             message\u001b[38;5;241m=\u001b[39m_ServerSelectionStatusMessage\u001b[38;5;241m.\u001b[39mFAILED,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    335\u001b[0m             failure\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_error_message(selector),\n\u001b[0;32m    336\u001b[0m         )\n\u001b[1;32m--> 337\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ServerSelectionTimeoutError(\n\u001b[0;32m    338\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_error_message(selector)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Timeout: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimeout\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124ms, Topology Description: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdescription\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    339\u001b[0m     )\n\u001b[0;32m    341\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m logged_waiting:\n\u001b[0;32m    342\u001b[0m     _debug_log(\n\u001b[0;32m    343\u001b[0m         _SERVER_SELECTION_LOGGER,\n\u001b[0;32m    344\u001b[0m         message\u001b[38;5;241m=\u001b[39m_ServerSelectionStatusMessage\u001b[38;5;241m.\u001b[39mWAITING,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    350\u001b[0m         remainingTimeMS\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mint\u001b[39m(end_time \u001b[38;5;241m-\u001b[39m time\u001b[38;5;241m.\u001b[39mmonotonic()),\n\u001b[0;32m    351\u001b[0m     )\n", "\u001b[1;31mServerSelectionTimeoutError\u001b[0m: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 67bf49ca5637ef94d4d74f30, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>"]}], "source": ["from pymongo import MongoClient\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(\"mongodb://************:27017/\")  # Update with your DB details if needed\n", "db = client[\"db-production\"]  # Replace with your database name\n", "collection = db[\"collection-resumes\"]  # Replace with your collection name\n", "query = {\n", "    \"Resume.PersonalInformation.BirthDate\": {\"$regex\": \"19800115\"}\n", "}\n", "\n", "# Fetch results\n", "results = collection.find(query)\n", "\n", "# Print results\n", "for result in results:\n", "    print(result)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["pymongo.synchronous.cursor.Cursor"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["type(results)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'FullName': '<PERSON><PERSON>', 'Gender': 'Male', 'BirthDate': 'May 10th, 2001', 'Email': '<EMAIL>', 'Address': 'SRUSHTI BUNGLOWS, Thaltej, Ahmedabad, Gujarat 380060', 'ContactNumber': '9763900816', 'LinkedInProfile': 'https://www.linkedin.com/in/tanajee-more-1002b6258'}\n"]}], "source": ["pipeline = [{'$limit': 1}]\n", "\n", "result = collection.aggregate(pipeline)\n", "for doc in result:\n", "    print(doc[\"Resume\"][\"PersonalInformation\"])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["pymongo.synchronous.command_cursor.CommandCursor"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["dictPipeline = {\n", "    \"$match\": {\n", "        \"$and\": [\n", "            {\n", "                \"Resume.PersonalInformation.BirthDate\": {\n", "                    \"$regex\": \"^\\d{4}04\"\n", "                }\n", "            },\n", "            {\n", "                \"$or\": [\n", "                    {\n", "                        \"Resume.Education.Degree\": {\n", "                            \"$regex\": \"msc\",\n", "                            \"$options\": \"i\"\n", "                        }\n", "                    },\n", "                    {\n", "                        \"Resume.Education.Degree\": {\n", "                            \"$regex\": \"master\",\n", "                            \"$options\": \"i\"\n", "                        }\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"$or\": [\n", "                    {\n", "                        \"Resume.Education.Degree\": {\n", "                            \"$regex\": \"physics\",\n", "                            \"$options\": \"i\"\n", "                        }\n", "                    },\n", "                    {\n", "                        \"Resume.Education.Degree\": {\n", "                            \"$regex\": \"physic\",\n", "                            \"$options\": \"i\"\n", "                        }\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from helperMongoDb import MongoDBClient"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to database: db-production\n"]}], "source": ["mongo_client = MongoDBClient()\n", "cursorMongoDb = mongo_client.execute_pipeline()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "envSqlAgent", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}