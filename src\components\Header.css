.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 72px; /* Increased height for less congestion */
  max-width: 100%;
}



/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 20px; /* Increased gap for better spacing */
}

/* Favorites Button */
.favorites-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorites-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: scale(1.05);
}

.favorites-btn:active {
  transform: scale(0.95);
}

.favorites-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--accent-primary);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Notifications */
.notification-container {
  position: relative;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 320px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
}

.notification-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-count {
  font-size: 12px;
  color: var(--accent-primary);
  font-weight: 500;
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.mark-all-read-btn:hover {
  background-color: var(--bg-tertiary);
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: background-color 0.2s ease;
  position: relative;
  cursor: pointer;
}

.notification-item:hover {
  background-color: var(--bg-tertiary);
}

.notification-item.unread {
  background-color: var(--bg-tertiary);
}

.notification-icon {
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.notification-delete {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.notification-delete:hover {
  background-color: var(--bg-secondary);
  color: var(--error-color);
}

.notification-loading,
.notification-empty {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.clear-all-btn {
  background: none;
  border: none;
  color: var(--error-color);
  font-size: 12px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.2s;
  width: 100%;
}

.clear-all-btn:hover {
  background-color: var(--error-color-light);
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-size: 14px;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  margin-top: 6px;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.view-all-btn {
  background: none;
  border: none;
  color: var(--accent-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.view-all-btn:hover {
  background-color: var(--bg-tertiary);
}

/* Theme Toggle Button */
.theme-toggle-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.theme-toggle-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

/* User Menu */
.user-menu-container {
  position: relative;
}

.user-menu-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.user-menu-btn:hover {
  background-color: var(--bg-tertiary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  overflow: hidden;
}

.user-avatar.large {
  width: 48px;
  height: 48px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.dropdown-icon {
  color: var(--text-tertiary);
  transition: transform 0.2s ease;
}

.user-menu-btn:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 280px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
}

.user-dropdown-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.user-details p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.user-dropdown-menu {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--bg-tertiary);
}

.dropdown-item.logout {
  color: var(--error);
}

.dropdown-item.logout:hover {
  background-color: var(--bg-tertiary);
}

.dropdown-divider {
  border: none;
  border-top: 1px solid var(--border-primary);
  margin: 8px 0;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .user-info {
    display: none;
  }

  .notification-dropdown,
  .user-dropdown {
    width: 280px;
    right: -20px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    gap: 8px;
  }

  .notification-dropdown,
  .user-dropdown {
    width: 260px;
    right: -40px;
  }
}
