/* CSS Variables for theming */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #334155;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --accent-primary: #3b82f6;
  --accent-secondary: #1d4ed8;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Dark theme variables */
.dark-theme {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb; /* Improved readability - lighter grey */
  --text-tertiary: #d1d5db; /* For less important text */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --accent-primary: #60a5fa;
  --accent-secondary: #3b82f6;
  --success: #34d399;
  --warning: #fbbf24;
  --error: #f87171;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Global Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
  background: var(--border-secondary);
}

.card {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  font-family: inherit;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.text-gray-500 {
  color: var(--text-tertiary);
}

.text-gray-600 {
  color: var(--text-secondary);
}

.text-gray-700 {
  color: var(--text-primary);
}

.text-blue-600 {
  color: var(--accent-primary);
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.p-4 {
  padding: 16px;
}

.p-6 {
  padding: 24px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.hidden {
  display: none;
}

/* Dark theme support for all pages */
.dark-theme .page-header h1,
.dark-theme .page-header h2,
.dark-theme .page-header h3 {
  color: var(--text-primary);
}

.dark-theme .page-header p {
  color: var(--text-secondary);
  text-align: center !important;
}

/* Support page specific styles */
.dark-theme .support-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.dark-theme .support-card h3 {
  color: var(--text-primary);
}

.dark-theme .support-card p {
  color: var(--text-secondary);
}

.dark-theme .faq-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .faq-question {
  color: var(--text-primary);
}

.dark-theme .faq-answer {
  color: var(--text-secondary);
}

/* Profile page specific styles */
.dark-theme .profile-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .profile-field label {
  color: var(--text-primary);
}

.dark-theme .profile-field-value {
  color: var(--text-secondary);
}

/* Activity log specific styles */
.dark-theme .activity-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .activity-title {
  color: var(--text-primary);
}

.dark-theme .activity-meta {
  color: var(--text-secondary);
}

/* Dashboard specific styles */
.dark-theme .stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .stat-value {
  color: var(--text-primary);
}

.dark-theme .stat-label {
  color: var(--text-secondary);
}

.dark-theme .quick-action-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .quick-action-title {
  color: var(--text-primary);
}

.dark-theme .quick-action-description {
  color: var(--text-secondary);
}

/* Upload page specific styles */
.dark-theme .upload-area {
  background: var(--bg-primary);
  border: 2px dashed var(--border-primary);
  color: var(--text-primary);
}

.dark-theme .upload-area:hover {
  border-color: var(--accent-primary);
  background: var(--bg-tertiary);
}

.dark-theme .upload-instructions {
  color: var(--text-secondary);
}

/* Search page specific styles */
.dark-theme .search-suggestions {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
}

.dark-theme .search-suggestion {
  color: var(--text-secondary);
}

.dark-theme .search-suggestion:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
